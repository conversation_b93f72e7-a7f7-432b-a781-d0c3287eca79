{"name": "drupal/recommended-project", "description": "Project template for Drupal projects with a relocated document root", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"composer/installers": "^2.0", "drupal/addtoany": "^2.0", "drupal/admin_toolbar": "^3.5", "drupal/better_exposed_filters": "^7.0", "drupal/config_pages": "^2.17", "drupal/core-composer-scaffold": "11.1.7", "drupal/core-project-message": "11.1.7", "drupal/core-recommended": "11.1.7", "drupal/crop": "^2.4", "drupal/custom_breadcrumbs": "^1.1", "drupal/devel": "^5.0", "drupal/dropdown_language": "^4.1", "drupal/easy_breadcrumb": "^2.0", "drupal/feeds": "^3.0", "drupal/image_widget_crop": "^3.0", "drupal/lang_dropdown": "^2.1", "drupal/menu_block": "^1.13", "drupal/menu_item_extras": "^3.1", "drupal/paragraphs": "^1.18", "drupal/pathauto": "^1.13", "drupal/recaptcha": "^3.4", "drupal/redirect": "^1.10", "drupal/rename_admin_paths": "^3.0", "drupal/scheduler": "^2.2", "drupal/search_api": "^1.37", "drupal/search_api_solr": "^4.3", "drupal/social_media": "^2.0", "drupal/social_media_links": "^2.10", "drupal/svg_image": "^3.2", "drupal/translatable_config_pages": "^1.0", "drupal/twig_tweak": "^3.4", "drupal/upgrade_status": "^4.0", "drupal/views_dependent_filters": "^1.3", "drupal/webform": "^6.3@beta", "drush/drush": "^13.6", "meilisearch/meilisearch-php": "^1.14", "phpoffice/phpspreadsheet": "^2.2"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "phpstan/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}, "sort-packages": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"]}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}, "require-dev": {"mglaman/drupal-check": "^1.5"}}